package sofa

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/chromedp/chromedp"
)

const (
	apiPrefix = "https://api.sofascore.com/api/v1"
)

var comps = map[string]int{
	"Champions League":           7,
	"Europa League":              679,
	"Europa Conference League":   17015,
	"EPL":                        17,
	"La Liga":                    8,
	"Bundesliga":                 35,
	"Serie A":                    23,
	"Ligue 1":                    34,
	"Turkish Super Lig":          52,
	"Argentina Liga Profesional": 155,
	"Argentina Copa de la Liga Profesional": 13475,
	"Liga 1 Peru":                406,
	"Copa Libertadores":          384,
	"MLS":                        242,
	"USL Championship":           13363,
	"USL1":                       13362,
	"USL2":                       13546,
	"Saudi Pro League":           955,
	"World Cup":                  16,
	"Euros":                      1,
	"Gold Cup":                   140,
	"Women's World Cup":          290,
}

type Sofascore struct{}

func NewSofascore() *Sofascore {
	return &Sofascore{}
}

func (s *Sofascore) sofaGet(url string) ([]byte, error) {
	ctx, cancel := chromedp.NewContext(context.Background())
	defer cancel()

	ctx, cancel = context.WithTimeout(ctx, 20*time.Second)
	defer cancel()

	var content string
	err := chromedp.Run(ctx,
		chromedp.Navigate(url),
		chromedp.Evaluate(`document.body.innerText`, &content),
	)

	if err != nil {
		return nil, fmt.Errorf("chromedp.Run failed: %w", err)
	}

	if content == "" {
		return nil, fmt.Errorf("fetched empty content from %s", url)
	}

	return []byte(content), nil
}

func (s *Sofascore) GetValidSeasons(league string) (map[string]int, error) {
	if _, ok := comps[league]; !ok {
		return nil, fmt.Errorf("invalid league: %s", league)
	}
	url := fmt.Sprintf("%s/unique-tournament/%d/seasons", apiPrefix, comps[league])
	body, err := s.sofaGet(url)
	if err != nil {
		return nil, err
	}

	var data struct {
		Seasons []struct {
			Year string `json:"year"`
			ID   int    `json:"id"`
		} `json:"seasons"`
	}

	if err := json.Unmarshal(body, &data); err != nil {
		return nil, fmt.Errorf("failed to unmarshal seasons: %w\nResponse: %s", err, string(body))
	}

	seasons := make(map[string]int)
	for _, season := range data.Seasons {
		seasons[season.Year] = season.ID
	}
	return seasons, nil
}

func (s *Sofascore) GetMatchDicts(year, league string) ([]map[string]interface{}, error) {
	validSeasons, err := s.GetValidSeasons(league)
	if err != nil {
		return nil, err
	}
	seasonID, ok := validSeasons[year]
	if !ok {
		return nil, fmt.Errorf("invalid year: %s for league: %s", year, league)
	}

	var matches []map[string]interface{}
	i := 0
	for {
		url := fmt.Sprintf("%s/unique-tournament/%d/season/%d/events/last/%d", apiPrefix, comps[league], seasonID, i)
		body, err := s.sofaGet(url)
		if err != nil {
			break
		}

		if !strings.HasPrefix(strings.TrimSpace(string(body)), "{") {
			break
		}

		var data map[string]interface{}
		if err := json.Unmarshal(body, &data); err != nil {
			break
		}

		eventsData, ok := data["events"]
		if !ok {
			break
		}

		events, ok := eventsData.([]interface{})
		if !ok || len(events) == 0 {
			break
		}

		for _, event := range events {
			matches = append(matches, event.(map[string]interface{}))
		}
		i++
	}
	return matches, nil
}

func (s *Sofascore) GetEventsByDate(date time.Time) ([]map[string]interface{}, error) {
	dateStr := date.Format("2006-01-02")
	url := fmt.Sprintf("%s/sport/football/scheduled-events/%s", apiPrefix, dateStr)
	body, err := s.sofaGet(url)
	if err != nil {
		return nil, err
	}

	if !strings.HasPrefix(strings.TrimSpace(string(body)), "{") {
		return nil, fmt.Errorf("invalid response from server: %s", string(body))
	}

	var data map[string]interface{}
	if err := json.Unmarshal(body, &data); err != nil {
		return nil, fmt.Errorf("failed to unmarshal events: %w\nResponse: %s", err, string(body))
	}

	eventsData, ok := data["events"]
	if !ok {
		return []map[string]interface{}{}, nil
	}

	events, ok := eventsData.([]interface{})
	if !ok {
		return nil, fmt.Errorf("events data is not a slice")
	}

	var result []map[string]interface{}
	for _, event := range events {
		result = append(result, event.(map[string]interface{}))
	}

	return result, nil
}

// ... other functions from the original file, adapted to use s.sofaGet ...

func (s *Sofascore) GetMatchIDFromURL(matchURL string) (int, error) {
	parts := strings.Split(matchURL, "#id:")
	if len(parts) != 2 {
		return 0, fmt.Errorf("invalid match URL: %s", matchURL)
	}
	return strconv.Atoi(parts[1])
}

func (s *Sofascore) GetMatchDict(matchID int) (map[string]interface{}, error) {
	url := fmt.Sprintf("%s/event/%d", apiPrefix, matchID)
	body, err := s.sofaGet(url)
	if err != nil {
		return nil, err
	}

	var data map[string]map[string]interface{}
	if err := json.Unmarshal(body, &data); err != nil {
		return nil, err
	}
	return data["event"], nil
}

func (s *Sofascore) GetTeamNames(matchID int) (string, string, error) {
	data, err := s.GetMatchDict(matchID)
	if err != nil {
		return "", "", err
	}
	homeTeam := data["homeTeam"].(map[string]interface{})["name"].(string)
	awayTeam := data["awayTeam"].(map[string]interface{})["name"].(string)
	return homeTeam, awayTeam, nil
}

func (s *Sofascore) GetPlayerIDs(matchID int) (map[string]int, error) {
	url := fmt.Sprintf("%s/event/%d/lineups", apiPrefix, matchID)
	body, err := s.sofaGet(url)
	if err != nil {
		return nil, err
	}

	var data map[string]map[string][]map[string]interface{}
	if err := json.Unmarshal(body, &data); err != nil {
		return nil, err
	}

	playerIDs := make(map[string]int)
	for _, team := range []string{"home", "away"} {
		for _, item := range data[team]["players"] {
			playerData := item["player"].(map[string]interface{})
			playerIDs[playerData["name"].(string)] = int(playerData["id"].(float64))
		}
	}
	return playerIDs, nil
}

func (s *Sofascore) ScrapeMatchMomentum(matchID int) ([]map[string]interface{}, error) {
	url := fmt.Sprintf("%s/event/%d/graph", apiPrefix, matchID)
	body, err := s.sofaGet(url)
	if err != nil {
		return nil, err
	}

	var data map[string][]map[string]interface{}
	if err := json.Unmarshal(body, &data); err != nil {
		return nil, err
	}
	return data["graphPoints"], nil
}

func (s *Sofascore) ScrapeTeamMatchStats(matchID int) ([]map[string]interface{}, error) {
	url := fmt.Sprintf("%s/event/%d/statistics", apiPrefix, matchID)
	body, err := s.sofaGet(url)
	if err != nil {
		return nil, err
	}

	var data map[string][]map[string]interface{}
	if err := json.Unmarshal(body, &data); err != nil {
		return nil, err
	}

	var stats []map[string]interface{}
	for _, period := range data["statistics"] {
		periodName := period["period"]
		for _, group := range period["groups"].([]interface{}) {
			groupMap := group.(map[string]interface{})
			groupName := groupMap["groupName"]
			for _, item := range groupMap["statisticsItems"].([]interface{}) {
				itemMap := item.(map[string]interface{})
				itemMap["period"] = periodName
				itemMap["group"] = groupName
				stats = append(stats, itemMap)
			}
		}
	}
	return stats, nil
}

func (s *Sofascore) ScrapePlayerMatchStats(matchID int) ([]map[string]interface{}, error) {
	matchDict, err := s.GetMatchDict(matchID)
	if err != nil {
		return nil, err
	}

	url := fmt.Sprintf("%s/event/%d/lineups", apiPrefix, matchID)
	body, err := s.sofaGet(url)
	if err != nil {
		return nil, err
	}

	var data map[string]map[string][]map[string]interface{}
	if err := json.Unmarshal(body, &data); err != nil {
		return nil, err
	}

	homePlayers := data["home"]["players"]
	awayPlayers := data["away"]["players"]

	for _, p := range homePlayers {
		p["teamId"] = matchDict["homeTeam"].(map[string]interface{})["id"]
		p["teamName"] = matchDict["homeTeam"].(map[string]interface{})["name"]
	}
	for _, p := range awayPlayers {
		p["teamId"] = matchDict["awayTeam"].(map[string]interface{})["id"]
		p["teamName"] = matchDict["awayTeam"].(map[string]interface{})["name"]
	}

	return append(homePlayers, awayPlayers...), nil
}

func (s *Sofascore) ScrapePlayerAveragePositions(matchID int) ([]map[string]interface{}, error) {
	homeName, awayName, err := s.GetTeamNames(matchID)
	if err != nil {
		return nil, err
	}

	url := fmt.Sprintf("%s/event/%d/average-positions", apiPrefix, matchID)
	body, err := s.sofaGet(url)
	if err != nil {
		return nil, err
	}

	var data map[string][]map[string]interface{}
	if err := json.Unmarshal(body, &data); err != nil {
		return nil, err
	}

	var positions []map[string]interface{}
	for key, name := range map[string]string{"home": homeName, "away": awayName} {
		for _, item := range data[key] {
			player := item["player"].(map[string]interface{})
			player["team"] = name
			player["averageX"] = item["averageX"]
			player["averageY"] = item["averageY"]
			positions = append(positions, player)
		}
	}
	return positions, nil
}

func (s *Sofascore) ScrapeHeatmaps(matchID int) (map[string]map[string]interface{}, error) {
	players, err := s.GetPlayerIDs(matchID)
	if err != nil {
		return nil, err
	}

	heatmaps := make(map[string]map[string]interface{})
	for name, id := range players {
		url := fmt.Sprintf("%s/event/%d/player/%d/heatmap", apiPrefix, matchID, id)
		body, err := s.sofaGet(url)
		if err != nil {
			// Player might not have a heatmap, so continue
			continue
		}

		var heatmapData map[string][]map[string]float64
		if err := json.Unmarshal(body, &heatmapData); err != nil {
			// Player might not have a heatmap, so continue
			continue
		}

		heatmaps[name] = map[string]interface{}{
			"id":      id,
			"heatmap": heatmapData["heatmap"],
		}
	}
	return heatmaps, nil
}

func (s *Sofascore) ScrapeMatchShots(matchID int) ([]map[string]interface{}, error) {
	url := fmt.Sprintf("%s/event/%d/shotmap", apiPrefix, matchID)
	body, err := s.sofaGet(url)
	if err != nil {
		return nil, err
	}

	var data map[string][]map[string]interface{}
	if err := json.Unmarshal(body, &data); err != nil {
		return nil, err
	}
	return data["shotmap"], nil
}

func (s *Sofascore) SearchPlayerOrTeam(query string) (map[string]interface{}, error) {
	url := fmt.Sprintf("%s/search/suggestions/player-team-persons?term=%s", apiPrefix, query)
	body, err := s.sofaGet(url)
	if err != nil {
		return nil, err
	}

	var data map[string]interface{}
	if err := json.Unmarshal(body, &data); err != nil {
		return nil, fmt.Errorf("failed to unmarshal search results: %w\nResponse: %s", err, string(body))
	}

	return data, nil
}
