package main

import (
	"log"
	"sofa"
	"time"

	_ "sofa/docs"

	swagger "github.com/swaggo/fiber-swagger"
	"github.com/gofiber/fiber/v2"
)

// @title Sofa API
// @version 1.0
// @description This is a sample server for a sofa application.
// @termsOfService http://swagger.io/terms/
// @contact.name API Support
// @contact.url http://www.swagger.io/support
// @contact.email <EMAIL>
// @license.name Apache 2.0
// @license.url http://www.apache.org/licenses/LICENSE-2.0.html
// @host localhost:3000
// @BasePath /
func main() {
	app := fiber.New()
	sofaClient := sofa.NewSofascore()

	app.Get("/swagger/*", swagger.WrapHandler)

	app.Get("/events/:date", func(c *fiber.Ctx) error {
		dateStr := c.Params("date")
		date, err := time.Parse("2006-01-02", dateStr)
		if err != nil {
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
				"error": "Invalid date format. Please use YYYY-MM-DD.",
			})
		}

		events, err := sofaClient.GetEventsByDate(date)
		if err != nil {
			return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
				"error": err.Error(),
			})
		}

		return c.JSON(events)
	})

	app.Get("/:league/:year", func(c *fiber.Ctx) error {
		league := c.Params("league")
		year := c.Params("year")

		matches, err := sofaClient.GetMatchDicts(year, league)
		if err != nil {
			return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
				"error": err.Error(),
			})
		}

		return c.JSON(matches)
	})

	app.Get("/:match_id/details", func(c *fiber.Ctx) error {
		matchID, err := c.ParamsInt("match_id")
		if err != nil {
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
				"error": "Invalid match ID",
			})
		}

		details, err := sofaClient.GetMatchDict(matchID)
		if err != nil {
			return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
				"error": err.Error(),
			})
		}

		return c.JSON(details)
	})

	app.Get("/:match_id/stats", func(c *fiber.Ctx) error {
		matchID, err := c.ParamsInt("match_id")
		if err != nil {
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
				"error": "Invalid match ID",
			})
		}

		stats, err := sofaClient.ScrapeTeamMatchStats(matchID)
		if err != nil {
			return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
				"error": err.Error(),
			})
		}

		return c.JSON(stats)
	})

	app.Get("/:match_id/lineups", func(c *fiber.Ctx) error {
		matchID, err := c.ParamsInt("match_id")
		if err != nil {
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
				"error": "Invalid match ID",
			})
		}

		lineups, err := sofaClient.ScrapePlayerMatchStats(matchID)
		if err != nil {
			return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
				"error": err.Error(),
			})
		}

		return c.JSON(lineups)
	})

	app.Get("/:match_id/momentum", func(c *fiber.Ctx) error {
		matchID, err := c.ParamsInt("match_id")
		if err != nil {
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
				"error": "Invalid match ID",
			})
		}

		momentum, err := sofaClient.ScrapeMatchMomentum(matchID)
		if err != nil {
			return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
				"error": err.Error(),
			})
		}

		return c.JSON(momentum)
	})

	app.Get("/:match_id/positions", func(c *fiber.Ctx) error {
		matchID, err := c.ParamsInt("match_id")
		if err != nil {
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
				"error": "Invalid match ID",
			})
		}

		positions, err := sofaClient.ScrapePlayerAveragePositions(matchID)
		if err != nil {
			return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
				"error": err.Error(),
			})
		}

		return c.JSON(positions)
	})

	app.Get("/:match_id/heatmaps", func(c *fiber.Ctx) error {
		matchID, err := c.ParamsInt("match_id")
		if err != nil {
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
				"error": "Invalid match ID",
			})
		}

		heatmaps, err := sofaClient.ScrapeHeatmaps(matchID)
		if err != nil {
			return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
				"error": err.Error(),
			})
		}

		return c.JSON(heatmaps)
	})

	app.Get("/:match_id/shots", func(c *fiber.Ctx) error {
		matchID, err := c.ParamsInt("match_id")
		if err != nil {
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
				"error": "Invalid match ID",
			})
		}

		shots, err := sofaClient.ScrapeMatchShots(matchID)
		if err != nil {
			return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
				"error": err.Error(),
			})
		}

		return c.JSON(shots)
	})

	app.Get("/search/:query", func(c *fiber.Ctx) error {
		query := c.Params("query")

		results, err := sofaClient.SearchPlayerOrTeam(query)
		if err != nil {
			return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
				"error": err.Error(),
			})
		}

		return c.JSON(results)
	})

	log.Fatal(app.Listen(":3000"))
}
