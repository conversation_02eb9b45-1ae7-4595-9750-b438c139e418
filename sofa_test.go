package sofa

import (
	"fmt"
	"testing"
	"time"
)

func TestGetValidSeasons(t *testing.T) {
	sofa := NewSofascore()
	seasons, err := sofa.GetValidSeasons("EPL")
	if err != nil {
		t.<PERSON><PERSON><PERSON>("GetValidSeasons() error = %v", err)
	}
	if len(seasons) == 0 {
		t.<PERSON><PERSON>("No seasons found")
	}
	fmt.Println(seasons)
}

func TestGetMatchDicts(t *testing.T) {
	sofa := NewSofascore()
	matches, err := sofa.GetMatchDicts("23/24", "EPL")
	if err != nil {
		t.Errorf("GetMatchDicts() error = %v", err)
	}
	if len(matches) == 0 {
		t.<PERSON><PERSON><PERSON>("No matches found")
	}
}

func TestGetEventsByDate(t *testing.T) {
	sofa := NewSofascore()
	date := time.Date(2025, time.July, 31, 0, 0, 0, 0, time.UTC)
	events, err := sofa.GetEventsByDate(date)
	if err != nil {
		t.<PERSON><PERSON><PERSON>("GetEventsByDate() error = %v", err)
	}
	if len(events) == 0 {
		t.<PERSON><PERSON><PERSON>("No events found")
	}
}
